export interface SuperAdminType {
  id: string;
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  role: string;
  isActive: boolean;
  phoneNumber: string;
  lastLoginAt: string;
  createdAt: string;
  updatedAt: string;
  permissions: PermissionType[];
}

export interface PermissionType {
  id: string;
  name: string;
  description: string;
  category: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}
