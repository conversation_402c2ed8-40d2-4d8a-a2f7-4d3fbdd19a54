"use client";

import React, { useEffect, useState, use<PERSON><PERSON>back, <PERSON> } from "react";
import Link from "next/link";
import Image from "next/image";
import { usePathname } from "next/navigation";

import { useSidebar } from "@/context/SidebarContext";
import { ChevronDownIcon } from "@/icons";
import {
  PiCalendarCheck,
  PiCar,
  PiChat,
  PiGauge,
  PiInfo,
  PiWallet,
} from "react-icons/pi";
import { TbBrandMercedes } from "react-icons/tb";
import { FiPhoneCall } from "react-icons/fi";

// Types
interface SubItem {
  name: string;
  path: string;
  pro?: boolean;
  new?: boolean;
}

interface NavItem {
  name: string;
  icon: React.ReactNode;
  path?: string;
  subItems?: SubItem[];
  roles?: Role[];
}

type Role = "superadmin" | "admin" | "driver" | "customer";

const allNavItems: NavItem[] = [
  {
    icon: <PiGauge size={20} />,
    name: "Dash<PERSON>",
    path: "/",
    roles: ["superadmin", "admin", "customer"],
  },
  {
    icon: <PiCar size={20} />,
    name: "My Clients",
    path: "/super-admin/My-clients",
    roles: ["superadmin", "admin"],
  },
  {
    icon: <TbBrandMercedes size={20} />,
    name: "Pricing",
    path: "/super-admin/pricing",
    roles: ["superadmin", "admin"],
  },
  {
    icon: <TbBrandMercedes size={20} />,
    name: "Setting",
    path: "/super-admin/setting",
    roles: ["superadmin", "admin"],
  },
  {
    name: "Dashboard",
    path: "/driver/dashboard",
    icon: <PiGauge size={20} />,
    roles: ["driver"],
  },
  {
    name: "Schedule",
    path: "/driver/schedule",
    icon: <PiCalendarCheck size={20} />,
    roles: ["driver"],
  },
  {
    name: "Chat",
    path: "/driver/chat",
    icon: <PiChat size={20} />,
    roles: ["driver"],
  },
  {
    name: "Report",
    path: "/driver/report",
    icon: <PiInfo size={20} />,
    roles: ["driver"],
  },
  {
    name: "My Earnings",
    path: "/driver/my-earnings",
    icon: <PiWallet size={20} />,
    roles: ["driver"],
  },
  {
    name: "Vehicle Info",
    path: "/driver/vehicle-info",
    icon: <PiCar size={20} />,
    roles: ["driver"],
  },
];

const getNavItemsByRole = (role: Role): NavItem[] => {
  return allNavItems.filter((item) => item.roles?.includes(role));
};

const AppSidebar: FC<{ role?: Role }> = ({ role = "driver" }) => {
  const { isExpanded, isMobileOpen, isHovered, setIsHovered } = useSidebar();
  const pathname = usePathname();

  const account =
    typeof window !== "undefined" ? localStorage.getItem("account") : null;
  const parsedAccount = account ? JSON.parse(account) : null;

  const firstRole = parsedAccount?.roles?.[0] ?? null;

  const navItems = getNavItemsByRole("superadmin");

  const othersItems: NavItem[] = []; // Add role-based if needed

  const [openSubmenu, setOpenSubmenu] = useState<{
    type: "main" | "others";
    index: number;
  } | null>(null);

  const isActive = useCallback((path: string) => path === pathname, [pathname]);

  const handleSubmenuToggle = (index: number, menuType: "main" | "others") => {
    setOpenSubmenu((prev) =>
      prev?.type === menuType && prev?.index === index
        ? null
        : { type: menuType, index },
    );
  };

  const renderMenuItems = (items: NavItem[], menuType: "main" | "others") => (
    <ul className="flex flex-col gap-3">
      {items.map((nav, idx) => {
        const isOpen =
          openSubmenu?.type === menuType && openSubmenu?.index === idx;
        const active = nav.path && isActive(nav.path);

        return (
          <li key={nav.name}>
            {nav.subItems ? (
              <button
                onClick={() => handleSubmenuToggle(idx, menuType)}
                className={`flex w-full items-center gap-4 rounded-md px-2 py-2 transition-all duration-200 ${isOpen ? "text-white" : "text-white/60 hover:text-white"} ${!isExpanded && !isHovered ? "lg:justify-center" : "lg:justify-start"}`}
              >
                <span className="text-xl">{nav.icon}</span>
                {(isExpanded || isHovered || isMobileOpen) && (
                  <>
                    <span
                      className={`font-medium ${isOpen ? "text-white" : "text-white/70"}`}
                    >
                      {nav.name}
                    </span>
                    <ChevronDownIcon
                      className={`ml-auto h-5 w-5 transition-transform duration-200 ${isOpen ? "rotate-180" : ""}`}
                    />
                  </>
                )}
              </button>
            ) : (
              nav.path && (
                <Link
                  href={nav.path}
                  className={`flex items-center gap-4 rounded-md px-2 py-2 transition-all duration-200 ${active ? "font-semibold text-white" : "text-white/60 hover:text-white"} ${!isExpanded && !isHovered ? "lg:justify-center" : "lg:justify-start"}`}
                >
                  <span className="text-xl">{nav.icon}</span>
                  {(isExpanded || isHovered || isMobileOpen) && (
                    <span className="font-medium">{nav.name}</span>
                  )}
                </Link>
              )
            )}
          </li>
        );
      })}
    </ul>
  );

  useEffect(() => {
    let matched = false;
    ["main", "others"].forEach((menuType) => {
      const items = menuType === "main" ? navItems : othersItems;
      items.forEach((nav, idx) => {
        nav.subItems?.forEach((sub) => {
          if (isActive(sub.path)) {
            setOpenSubmenu({ type: menuType as "main" | "others", index: idx });
            matched = true;
          }
        });
      });
    });

    if (!matched) setOpenSubmenu(null);
  }, [pathname, isActive, navItems, othersItems]);

  return (
    <aside
      className={`fixed top-0 left-0 z-40 mt-16 h-screen border-r border-gray-200 px-5 text-gray-900 transition-all duration-300 lg:mt-0 dark:border-gray-800 dark:bg-gray-900 ${isExpanded || isMobileOpen || isHovered ? "w-[236px]" : "w-[90px]"} ${isMobileOpen ? "translate-x-0" : "-translate-x-full"} lg:translate-x-0`}
      // style={{
      //     background:
      //         "linear-gradient(-153deg, rgb(24, 236, 149), rgb(37, 138, 187), rgb(55, 7, 239), rgb(112, 24, 235))",
      // }}

      style={{
        backgroundImage: "url('/images/Side-Bar.svg')",
        backgroundSize: "cover",
        backgroundRepeat: "no-repeat",
        backgroundPosition: "bottom",
      }}
      onMouseEnter={() => !isExpanded && setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div
        className={`flex py-8 ${!isExpanded && !isHovered ? "lg:justify-center" : "justify-start"}`}
      >
        <Link href="/">
          {isExpanded || isHovered || isMobileOpen ? (
            <>
              <Image
                className="dark:hidden"
                src="/images/sidebar/logo.png"
                alt="Logo"
                width={150}
                height={40}
              />
              <Image
                className="hidden dark:block"
                src="/images/sidebar/logo.png"
                alt="Logo"
                width={150}
                height={40}
              />
            </>
          ) : (
            <Image
              src="/images/sidebar/logo-mini.png"
              alt="Logo"
              width={32}
              height={32}
            />
          )}
        </Link>
      </div>

      <div className="no-scrollbar flex flex-col overflow-y-auto">
        <nav className="mb-6">
          <div className="flex flex-col gap-4">
            {renderMenuItems(navItems, "main")}
          </div>
          <div className="py-6">
            <button className="flex w-full items-center justify-center gap-3 rounded-full bg-[#FF5F5F] px-5 py-2 text-[14px] font-normal text-white shadow">
              <span className="rounded-full bg-white p-[8px] text-[#050013]">
                <FiPhoneCall size={20} />
              </span>
              Emergency support 24/7
            </button>
          </div>
        </nav>
        {/* <SideMenuBottom className="absolute left-[-1px] bottom-0" /> */}
      </div>
    </aside>
  );
};

export default AppSidebar;
