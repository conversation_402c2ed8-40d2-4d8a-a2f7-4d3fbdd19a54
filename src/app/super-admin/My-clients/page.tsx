"use client";

import React, { useEffect, useRef, useState } from "react";
import { Lu<PERSON>encil } from "react-icons/lu";
import { FaChevronDown, FaCheck } from "react-icons/fa6";
import { RxCounterClockwiseClock } from "react-icons/rx";
import { FiFilter, FiDownload, FiTrash2 } from "react-icons/fi";
import { IoIosSearch } from "react-icons/io";
import { HiPlus } from "react-icons/hi";
import Link from "next/link";
import { PermissionType, SuperAdminType } from "@/utils/types";
import { useRouter } from "next/navigation";
import { backendApiClient } from "@/utils/apiClient";
import { useMutation, useQuery } from "@tanstack/react-query";
import { formatDate } from "@/utils/commonFunction";
import { toast } from "react-toastify";
import { queryClient } from "@/hooks/useGlobalContext";

/* ----------------------------- Types ----------------------------- */

type SuperAdminsResponse = {
  success: boolean;
  data: SuperAdminType[];
  meta: unknown;
};

/* ----------------------- Accordion Section ------------------------ */

function AccordionSection({
  title,
  items,
  onPress,
  selectedPermissions,
}: {
  title: string;
  items: { id: string; description: string }[];
  onPress: (id: string) => void;
  selectedPermissions: string[];
}) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="mb-4">
      <div
        className="flex cursor-pointer items-center justify-between pb-2"
        onClick={() => setIsOpen(!isOpen)}
      >
        <label className="flex cursor-pointer items-center gap-3">
          <div className="relative flex items-center">
            {/* Optional "section checkbox" for future "select all" — not wired */}
            <input type="checkbox" className="peer hidden" readOnly />
            <span className="flex h-4 w-4 items-center justify-center rounded-sm border border-gray-400 text-xs font-bold text-white peer-checked:border-[#3324E3] peer-checked:bg-[#3324E3]" />
            <FaCheck
              size={12}
              className="absolute left-[2px] transition-opacity duration-200 peer-checked:text-[#18EC94] peer-checked:opacity-100 peer-[&:not(:checked)]:opacity-0"
            />
          </div>
          <span className="text-[14px] font-medium text-[#050013] peer-checked:text-[#050013]">
            {title}
          </span>
        </label>
        <span className="bg-tables rounded-full p-1">
          <FaChevronDown
            className={`h-3 w-3 text-gray-500 transition-transform duration-200 ${
              isOpen ? "rotate-180" : "rotate-0"
            }`}
          />
        </span>
      </div>

      {isOpen && (
        <div className="mt-2 mb-6">
          {items.map((item) => {
            const checked = selectedPermissions?.includes(item.id);
            return (
              <label
                key={item.id}
                className="mb-2 flex cursor-pointer items-center space-x-2"
              >
                <div className="relative flex items-center">
                  <input
                    type="checkbox"
                    className="peer hidden"
                    checked={checked}
                    onChange={(e) => {
                      e.stopPropagation();
                      onPress(item.id);
                    }}
                  />
                  <span className="flex h-4 w-4 items-center justify-center rounded-sm border border-gray-400 text-xs font-bold text-white peer-checked:border-[#3324E3] peer-checked:bg-[#3324E3]" />
                  <FaCheck
                    size={12}
                    className={`absolute left-[2px] transition-opacity duration-200 ${
                      checked ? "text-[#18EC94] opacity-100" : "opacity-0"
                    }`}
                  />
                </div>
                <span className="text-sm text-[#76787A]">
                  {item.description}
                </span>
              </label>
            );
          })}
        </div>
      )}
    </div>
  );
}

/* -------------------------- Mock lists --------------------------- */

const superAdmins = [
  { date: "11-01-2024", name: "John", status: "Active" },
  { date: "11-01-2024", name: "Dave", status: "Active" },
  { date: "11-01-2024", name: "Monty", status: "Active" },
  { date: "26-12-2024", name: "Sam", status: "Inactive" },
];

const clients = [
  {
    icon: "SL",
    date: "10-02-2024",
    name: "Silverline Solutions",
    status: "Active",
  },
  { icon: "FE", date: "12-02-2024", name: "FleetEdge", status: "Inactive" },
];

/* ---------------------- Permission Sections ---------------------- */
/* IDs match your backend examples so pre-checks work */
const accessSections = [
  {
    title: "User & Role Management",
    items: [
      {
        id: "17850a88-22e1-4258-99ab-1d23c3c15605",
        description: "Create, edit, delete, Dispatchers, Drivers, Users",
      },
      {
        id: "f9c41175-4790-4db3-ba3c-ea49178fe585",
        description: "Assign Admins to taxi companies",
      },
      // extra examples (not in your sample data)
      {
        id: "b2d344a6-6104-4001-a646-b94c1dcff871",
        description: "Manage permissions for all roles",
      },
      {
        id: "896ae7ba-4b33-4c64-a229-3ffaeb2451ee",
        description: "Suspend/reactivate any user",
      },
    ],
  },
  {
    title: "Booking & Dispatch Control",
    items: [
      {
        id: "6a94ea9b-74f9-446a-b833-f77d8e57454d",
        description: "View, edit, and manage all bookings",
      },
      {
        id: "73e2a895-735f-4edf-bf71-2bfc73e4d3c2",
        description: "Assign/reassign drivers to rides",
      },
      {
        id: "912b2400-d29d-4367-a0bf-344d1f9bee73",
        description: "View driver live locations",
      },
      {
        id: "887f7358-e6a4-4e7e-b4a6-33b0754151e0",
        description: "Cancel or reschedule any ride",
      },
    ],
  },
  {
    title: "Company & Financial Management",
    items: [
      {
        id: "91f16da7-365a-4481-9601-7cc38ef9ce18",
        description: "Add, edit, or remove taxi companies",
      },
      {
        id: "cc025be7-6521-4b3f-83d1-984a107402b7",
        description: "Manage company subscription plans",
      },
      {
        id: "1c6f7aa9-6fed-44c3-b85a-cb0564030308",
        description:
          "View & modify company-specific pricing and fare structures",
      },
      {
        id: "63f54082-2563-4ede-8c91-840d73f7236f",
        description: "Access & edit company billing information",
      },
    ],
  },
  {
    title: "System & Policy Settings",
    items: [
      {
        id: "817105ed-b723-44f9-b02b-a2a553de0878",
        description: "Define platform-wide fare policies",
      },
      {
        id: "e060e981-76ca-4cf3-8b89-431ae7083f3b",
        description: "Set geofencing rules & restrictions",
      },
      {
        id: "8e3e677a-7f22-444e-8d5e-c4266b1225c3",
        description: "Control global discount and promo policies",
      },
      {
        id: "f2757189-f194-48e1-93fc-91e9945d0428",
        description: "Configure ride cancellation policies",
      },
    ],
  },
  {
    title: "Reporting & Analytics",
    items: [
      {
        id: "01af9c39-1eaf-4ade-8996-c236ea245a8e",
        description:
          "View and export reports on revenue, ride activity, and system performance",
      },
      {
        id: "08131c94-2341-4560-bc35-b671fb834fae",
        description: "Monitor driver performance & customer ratings",
      },
      {
        id: "28d21585-e20d-48cb-b364-b9bad38ff6c0",
        description: "Analyze dispatcher efficiency",
      },
    ],
  },
];

/* ============================ Page ============================ */

export default function MyClients() {
  const [activeTab, setActiveTab] = useState<"Super Admins" | "My Clients">(
    "Super Admins",
  );
  const [search, setSearch] = useState("");
  const [isModalOpen, setIsModalOpen] = useState(false);

  const [selectedUser, setSelectedUser] = useState<SuperAdminType | null>(null);
  const [editUser, setEditUser] = useState<SuperAdminType | null>(null);

  const modalRef = useRef<HTMLDivElement | null>(null);
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);
  const [editIsActive, setEditIsActive] = useState<boolean>(true);

  const { push } = useRouter();

  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "test",
    email: "",
    password: "test123456",
    role: "super_admin",
    phoneNumber: "+87655945645",
  });

  const currentData = activeTab === "Super Admins" ? superAdmins : clients;
  const filteredData = currentData.filter((row) =>
    row.name.toLowerCase().includes(search.toLowerCase()),
  );

  /* --------------------------- Queries --------------------------- */

  const getSuperAdmins = () => {
    return backendApiClient.get("superadmin").json<SuperAdminsResponse>();
  };

  const {
    data: superAdminsData,
    isLoading,
    error,
  } = useQuery<SuperAdminsResponse>({
    queryKey: ["superAdmins"],
    queryFn: getSuperAdmins,
    enabled: true,
  });

  /* ------------------- Permission selection ------------------- */

  function handleCheckboxPress(id: string) {
    setSelectedPermissions((prev) =>
      prev.includes(id) ? prev.filter((pid) => pid !== id) : [...prev, id],
    );
  }

  /* ---------------------------- Add ---------------------------- */

  async function addSuperAdmin(data: any) {
    return await backendApiClient.post("superadmin", { json: data }).json();
  }

  const { mutate: addSuperAdminMutation } = useMutation({
    mutationFn: addSuperAdmin,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["superAdmins"] });
      setIsModalOpen(false);
      setSelectedPermissions([]);
      setFormData({
        firstName: "",
        lastName: "test",
        email: "",
        password: "",
        role: "super_admin",
        phoneNumber: "+87655945645",
      });
      toast.success("Super Admin Added Successfully", {
        autoClose: 5000,
        position: "top-center",
      });
    },
    onError: (err) => {
      console.error(err);
      toast.error("Failed to add Super Admin");
    },
  });

  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault();

    if (!formData.firstName || !formData.email) {
      toast.error("Please fill in all fields");
      return;
    }
    if (selectedPermissions.length === 0) {
      toast.error("Please select at least one permission");
      return;
    }
    const dataToSend = {
      ...formData,
      permissionIds: selectedPermissions,
    };

    addSuperAdminMutation(dataToSend);
  }

  /* --------------------------- Delete -------------------------- */

  async function deleteSuperAdmin(id: string) {
    return await backendApiClient.delete(`superadmin/${id}`).json();
  }

  const { mutate: deleteSuperAdminMutation } = useMutation({
    mutationFn: deleteSuperAdmin,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["superAdmins"] });
      toast.success("Super Admin Deleted Successfully", {
        autoClose: 5000,
        position: "top-center",
      });
    },
    onError: (err) => {
      console.error(err);
      toast.error("Failed to delete Super Admin");
    },
  });

  /* --------------------------- Update -------------------------- */

  async function updateSuperAdmin({
    id,
    payload,
  }: {
    id: string;
    payload: { permissionIds?: string[]; isActive?: boolean };
  }) {
    return await backendApiClient
      .patch(`superadmin/${id}`, { json: payload })
      .json();
  }

  const { mutate: updateSuperAdminMutation, isPending: isUpdating } =
    useMutation({
      mutationFn: updateSuperAdmin,
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ["superAdmins"] });
        toast.success("Super Admin Updated Successfully", {
          autoClose: 5000,
          position: "top-center",
        });
        setEditUser(null);
        setSelectedPermissions([]);
      },
      onError: (err) => {
        console.error(err);
        toast.error("Failed to update Super Admin");
      },
    });

  const handleSaveEdit = () => {
    if (!editUser) return;

    updateSuperAdminMutation({
      id: editUser.id,
      payload: {
        permissionIds: selectedPermissions,
      },
    });
  };

  /* When opening edit, prime permission IDs + isActive */
  useEffect(() => {
    if (editUser) {
      setSelectedPermissions(editUser.permissions?.map((p) => p.id) ?? []);
      setEditIsActive(!!editUser.isActive);
    }
  }, [editUser]);

  return (
    <div className="p-6">
      {/* Tabs */}
      <div className="flex items-center justify-between">
        <div className="flex gap-6">
          {["Super Admins", "My Clients"].map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab as "Super Admins" | "My Clients")}
              className={`relative pb-3 text-[13px] ${
                activeTab === tab
                  ? "font-semibold text-[#3324E3] after:absolute after:bottom-0 after:left-0 after:h-[3px] after:w-full after:rounded-full after:bg-[#3324E3] after:content-['']"
                  : "text-[#76787A]"
              }`}
            >
              {tab}
            </button>
          ))}
        </div>
        <button
          onClick={() => {
            if (activeTab === "Super Admins") {
              setIsModalOpen(true);
            } else {
              push("/super-admin/add-new-client/");
            }
          }}
          className="flex items-center gap-2 rounded-full bg-[#3324E3] px-4 py-2 text-xs font-medium text-white sm:text-sm"
        >
          <HiPlus />
          New {activeTab === "Super Admins" ? "Super Admin" : "Client"}
        </button>
      </div>

      {/* Table */}
      <div className="mt-6 overflow-visible rounded-xl border border-gray-200 bg-white">
        <div className="header-bar bg-table-head flex items-center justify-between rounded-t-[12px] px-3 py-1">
          <form
            className="max-w-md flex-1"
            onSubmit={(e) => e.preventDefault()}
          >
            <label className="sr-only mb-2 text-sm font-medium text-gray-900">
              Search
            </label>
            <div className="relative">
              <div className="pointer-events-none absolute inset-y-0 start-0 flex items-center ps-3">
                <IoIosSearch size={20} />
              </div>
              <input
                id="default-search"
                type="search"
                placeholder="Search here"
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="block w-3/4 rounded-full border border-gray-300 bg-white p-2 ps-10 text-sm text-gray-900 shadow-[0_10px_40px_0_#0000000D] focus:border-blue-500 focus:ring-blue-500"
              />
            </div>
          </form>

          <div className="flex items-center gap-2">
            <button
              type="button"
              className="flex items-center gap-1.5 rounded-full border border-gray-200 bg-white px-3 py-2 text-xs font-medium text-[#13BB76] hover:bg-gray-100 focus:outline-none"
            >
              <span className="h-2 w-2 rounded-full bg-[#13BB76]" />
              Active
            </button>

            <button
              type="button"
              className="flex items-center gap-1.5 rounded-full border border-gray-200 bg-white px-3 py-2 text-xs font-medium text-[#76787A] hover:bg-gray-100 focus:outline-none"
            >
              <span className="h-2 w-2 rounded-full bg-[#76787A]" />
              Inactive
            </button>

            <div className="relative inline-block text-left">
              <button
                type="button"
                className="flex items-center gap-2 rounded-full border border-gray-200 bg-white px-3 py-2 text-xs font-medium text-[#76787A] hover:bg-gray-100"
              >
                <FiFilter size={18} />
                Filters
              </button>
            </div>

            <button
              aria-label="clock"
              type="button"
              className="flex items-center justify-center rounded-full border border-gray-200 bg-white p-1 text-[#76787A] hover:bg-gray-100"
            >
              <RxCounterClockwiseClock size={22} />
            </button>

            <button
              aria-label="download"
              type="button"
              className="flex items-center justify-center rounded-full border border-gray-200 bg-white p-2 text-[#76787A] hover:bg-gray-100"
            >
              <FiDownload size={16} />
            </button>
          </div>
        </div>

        <div className="custom-scrollbar max-w-full overflow-x-auto">
          <div className="max-w-[992px] min-w-[-webkit-fill-available]">
            <table className="min-w-full table-auto text-left text-[11px]">
              <thead className="text-[#76787A]">
                {activeTab === "Super Admins" ? (
                  <tr>
                    <th className="px-4 py-3 font-medium">Date</th>
                    <th className="px-4 py-3 font-medium">User Name</th>
                    <th className="px-4 py-3 font-medium">Role Name</th>
                    <th className="px-4 py-3 font-medium">Status</th>
                    <th className="px-4 py-3">Action</th>
                  </tr>
                ) : (
                  <tr>
                    <th className="px-4 py-3"></th>
                    <th className="px-4 py-3 font-medium text-nowrap">
                      Client Name
                    </th>
                    <th className="px-4 py-3 font-medium text-nowrap">
                      Date Added
                    </th>
                    <th className="px-4 py-3 font-medium text-nowrap">
                      Primary Contact
                    </th>
                    <th className="px-4 py-3 font-medium text-nowrap">
                      Permissions
                    </th>
                    <th className="px-4 py-3 font-medium text-nowrap">
                      No of Dispatchers
                    </th>
                    <th className="px-4 py-3 font-medium text-nowrap">
                      No of Drivers
                    </th>
                    <th className="px-4 py-3 font-medium text-nowrap">
                      No of Passengers
                    </th>
                    <th className="px-4 py-3 font-medium text-nowrap">
                      No of Clients
                    </th>
                    <th className="px-4 py-3 font-medium text-nowrap">
                      Status
                    </th>
                    <th className="px-4 py-3 font-medium text-nowrap">
                      Subscription Plan
                    </th>
                    <th className="px-4 py-3"></th>
                  </tr>
                )}
              </thead>

              <tbody className="divide-y divide-gray-100">
                {activeTab === "Super Admins"
                  ? superAdminsData?.data
                      ?.filter((row) =>
                        `${row.firstName} ${row.lastName}`
                          .toLowerCase()
                          .includes(search.toLowerCase()),
                      )
                      .map((row) => (
                        <tr key={row.id} className="hover:bg-[#E4FFF4]">
                          <td className="px-4 py-3 text-[#050013]">
                            {formatDate(row.createdAt)}
                          </td>
                          <td
                            className="cursor-pointer px-4 py-3 font-medium text-[#050013]"
                            onClick={() => setSelectedUser(row)}
                          >
                            {row.firstName} {row.lastName}
                          </td>
                          <td className="px-4 py-3 text-[#050013]">
                            {row.role}
                          </td>
                          <td className="px-4 py-3">
                            <span
                              className={`flex items-center gap-1 ${
                                row.isActive
                                  ? "text-[#13BB76]"
                                  : "text-[#8C8B9F]"
                              }`}
                            >
                              <span className="h-[6px] w-[6px] rounded-full bg-current" />
                              {row.isActive ? "Active" : "Inactive"}
                            </span>
                          </td>
                          <td className="px-4 py-3">
                            <div className="flex justify-center gap-3">
                              <LuPencil
                                className="h-4 w-4 cursor-pointer text-[#050013]"
                                onClick={() => {
                                  setEditUser(row);
                                }}
                              />
                              <FiTrash2
                                onClick={() => {
                                  deleteSuperAdminMutation(row.id);
                                }}
                                className="h-4 w-4 cursor-pointer text-[#050013]"
                              />
                            </div>
                          </td>
                        </tr>
                      ))
                  : filteredData.map((row, index) => (
                      <tr key={index} className="hover:bg-[#E4FFF4]">
                        <td className="p-3 px-4 py-3 text-nowrap text-[#050013]">
                          {row.date}
                        </td>
                        <td className="px-4 py-3 text-nowrap text-[#050013]">
                          <Link
                            href={`/super-admin/view-client`}
                            className="text-[#050013]"
                          >
                            {row.name}
                          </Link>
                        </td>
                        <td className="px-4 py-3 text-nowrap text-[#050013]">
                          {row.date}
                        </td>
                        <td className="px-4 py-3 text-nowrap text-[#050013]">
                          Oliver Thompson
                        </td>
                        <td className="px-4 py-3 text-nowrap text-[#050013]">
                          Full Access
                        </td>
                        <td className="px-4 py-3 text-nowrap text-[#050013]">
                          02
                        </td>
                        <td className="px-4 py-3 text-nowrap text-[#050013]">
                          02
                        </td>
                        <td className="px-4 py-3 text-nowrap text-[#050013]">
                          17
                        </td>
                        <td className="px-4 py-3 text-nowrap text-[#050013]">
                          02
                        </td>
                        <td className="px-4 py-3">
                          <span
                            className={`flex items-center gap-1 ${
                              row.status === "Active"
                                ? "text-[#13BB76]"
                                : "text-[#8C8B9F]"
                            }`}
                          >
                            <span className="h-[6px] w-[6px] rounded-full bg-current" />
                            {row.status}
                          </span>
                        </td>
                        <td className="px-4 py-3 text-nowrap text-[#050013]">
                          Basic
                        </td>
                        <td className="px-4 py-3">
                          <div className="flex justify-center gap-3">
                            <LuPencil className="h-4 w-4 cursor-pointer text-[#050013]" />
                            <FiTrash2 className="h-4 w-4 cursor-pointer text-[#050013]" />
                          </div>
                        </td>
                      </tr>
                    ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* add new super admin modal */}
      {isModalOpen && (
        <div className="custom-scrollbar fixed inset-0 z-999 flex">
          <div className="absolute inset-0 bg-black/30"></div>
          <div
            ref={modalRef}
            className="ml-auto h-full w-full max-w-md translate-x-0 transform overflow-y-auto rounded-tl-[30px] rounded-bl-[30px] bg-white shadow-xl transition-transform duration-300 ease-out"
          >
            <div className="bg-tables mb-4 flex items-center justify-between px-6 py-5">
              <h2 className="text-[20px] font-normal text-[#050013]">
                New Super Admins
              </h2>
              <button
                onClick={() => setIsModalOpen(false)}
                className="text-gray-500 hover:text-red-500"
              >
                ✕
              </button>
            </div>

            <form className="flex flex-col gap-6" onSubmit={handleSubmit}>
              <div className="px-6 py-3">
                <h3 className="mb-2 text-[14px] font-medium text-[#050013]">
                  Basic Information
                </h3>
                <input
                  type="text"
                  required
                  onChange={(e) =>
                    setFormData({ ...formData, firstName: e.target.value })
                  }
                  placeholder="Full Name"
                  className="mb-3 w-full rounded-md border p-2 text-[13px] text-[#76787A] outline-none focus:ring-2 focus:ring-blue-500"
                />
                <input
                  type="email"
                  onChange={(e) =>
                    setFormData({ ...formData, email: e.target.value })
                  }
                  required
                  placeholder="Email ID"
                  className="w-full rounded-md border p-2 text-[13px] text-[#76787A] outline-none focus:ring-2 focus:ring-blue-500"
                />
                <input
                  type="password"
                  autoComplete="off"
                  onChange={(e) =>
                    setFormData({ ...formData, password: e.target.value })
                  }
                  required
                  placeholder="password"
                  className="mt-3 w-full rounded-md border p-2 text-[13px] text-[#76787A] outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div className="px-6 py-3">
                <h3 className="mb-4 text-[14px] font-medium text-[#050013]">
                  Access Control and Permissions
                </h3>
                {accessSections.map((section, idx) => (
                  <AccordionSection
                    key={idx}
                    title={section.title}
                    items={section.items}
                    onPress={handleCheckboxPress}
                    selectedPermissions={selectedPermissions}
                  />
                ))}
              </div>

              <div className="sticky bottom-0 border-t bg-white px-6 py-3">
                <button
                  type="submit"
                  className="float-right rounded-full bg-[#3707EF] px-6 py-2 text-white hover:bg-[#3d0cc0]"
                >
                  Add
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* View detail modal  */}
      {selectedUser && (
        <div className="custom-scrollbar fixed inset-0 z-999 flex">
          <div
            className="absolute inset-0 bg-black/30"
            onClick={() => setSelectedUser(null)}
          />
          <div className="ml-auto h-full w-full max-w-md translate-x-0 transform overflow-y-auto rounded-tl-[30px] rounded-bl-[30px] bg-white shadow-xl transition-transform duration-300 ease-out">
            <div className="bg-tables mb-4 flex items-center justify-between px-6 py-5">
              <h2 className="text-[20px] font-normal text-[#050013]">
                View User Details
              </h2>
              <button
                onClick={() => setSelectedUser(null)}
                className="text-gray-500 hover:text-red-500"
              >
                ✕
              </button>
            </div>

            <div className="bg-tables space-y-4 px-6 py-6 text-[13px]">
              <div>
                <h3 className="mb-2 rounded-full bg-white px-4 py-2 text-[14px] font-medium text-[#050013]">
                  General Details
                </h3>

                <div className="grid grid-cols-2 gap-2 px-4 py-4">
                  <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                    <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                      User Name:
                    </span>{" "}
                    {selectedUser.firstName} {selectedUser.lastName}
                  </div>
                  <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                    <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                      Role Name:
                    </span>{" "}
                    {selectedUser.role}
                  </div>
                  <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                    <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                      Created Date:
                    </span>{" "}
                    {formatDate(selectedUser.createdAt)}
                  </div>
                  <div className="mb-4 grid text-[12px] font-medium text-[#050013]">
                    <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                      Status:
                    </span>
                    <span
                      className={`ml-1 inline-flex items-center gap-1 ${
                        selectedUser.isActive
                          ? "text-[#13BB76]"
                          : "text-[#8C8B9F]"
                      }`}
                    >
                      <span className="inline-block h-2 w-2 rounded-full bg-current" />
                      {selectedUser.isActive ? "Active" : "Inactive"}
                    </span>
                  </div>
                  <div className="mb-4 grid text-[14px] font-medium text-[#13BB76]">
                    <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                      Last Active:
                    </span>
                    {selectedUser.lastLoginAt
                      ? formatDate(selectedUser.lastLoginAt)
                      : "—"}
                  </div>
                </div>
              </div>

              <div className="mb-12">
                <h3 className="rounded-full bg-white px-4 py-2 text-[14px] font-medium text-[#050013]">
                  Access Control And Permissions
                </h3>
                <div className="px-6 py-3">
                  <ul className="ml-5 list-disc text-[#050013]">
                    {selectedUser.permissions?.map((p) => (
                      <li key={p.id} className="my-1">
                        {p.description}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>

              <div>
                <h3 className="mb-1 rounded-full bg-white px-4 py-2 text-[14px] font-medium text-[#050013]">
                  Edit Logs
                </h3>
                <div className="grid grid-cols-2 gap-2 px-4 py-4">
                  <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                    <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                      Last Modified:
                    </span>{" "}
                    {formatDate(selectedUser.updatedAt)}
                  </div>
                  <div className="mb-4 grid text-[14px] font-medium text-[#050013]">
                    <span className="mb-1 text-[13px] font-normal text-[#76787A]">
                      Last Modified By:
                    </span>{" "}
                    —
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* edit modal  */}
      {editUser && (
        <div className="custom-scrollbar fixed inset-0 z-999 flex">
          <div
            className="absolute inset-0 bg-black/30"
            onClick={() => setEditUser(null)}
          />
          <div className="ml-auto h-full w-full max-w-md translate-x-0 transform overflow-y-auto rounded-tl-[30px] rounded-bl-[30px] bg-white shadow-xl transition-transform duration-300 ease-out">
            <div className="bg-tables mb-4 flex items-center justify-between px-6 py-5">
              <h2 className="text-[20px] font-normal text-[#050013]">
                Edit User Details
              </h2>
              <button
                onClick={() => {
                  setEditUser(null);
                  setSelectedPermissions([]);
                }}
                className="text-gray-500 hover:text-red-500"
              >
                ✕
              </button>
            </div>

            {/* General Details */}
            <div className="bg-tables space-y-4 px-6 py-6 text-[13px]">
              <div>
                <h3 className="mb-2 rounded-full bg-white px-4 py-2 text-[14px] font-medium text-[#050013]">
                  General Details
                </h3>
                <div className="grid grid-cols-2 gap-4 px-4 py-4">
                  {/* User Name (read-only combined for now) */}
                  <div className="relative w-full">
                    <input
                      type="text"
                      id="username"
                      value={`${editUser.firstName} ${editUser.lastName}`}
                      readOnly
                      placeholder="User Name"
                      className="peer w-full rounded-md border border-gray-300 px-3 pt-4 pb-2 text-sm text-[#050013] placeholder-transparent focus:ring-2 focus:ring-[#3324E3] focus:outline-none"
                    />
                    <label
                      htmlFor="username"
                      className="bg-tables absolute -top-2 left-3 px-[4px] py-[1px] text-[11px] text-[#76787A]"
                    >
                      User Name
                    </label>
                  </div>

                  {/* Role Name (read-only) */}
                  <div className="relative w-full">
                    <input
                      id="role"
                      value={editUser.role}
                      readOnly
                      className="peer w-full rounded-md border border-gray-300 px-3 pt-4 pb-2 text-sm text-[#050013] focus:ring-2 focus:ring-[#3324E3] focus:outline-none"
                    />
                    <label
                      htmlFor="role"
                      className="bg-tables absolute -top-2 left-3 px-[4px] py-[1px] text-[11px] text-[#76787A]"
                    >
                      Role Name
                    </label>
                  </div>

                  {/* Created Date */}
                  <div className="relative w-full">
                    <input
                      type="text"
                      id="createdDate"
                      value={formatDate(editUser.createdAt)}
                      readOnly
                      placeholder="Created Date"
                      className="peer w-full rounded-md border border-gray-300 px-3 pt-4 pb-2 text-sm text-[#050013] placeholder-transparent focus:ring-2 focus:ring-[#3324E3] focus:outline-none"
                    />
                    <label
                      htmlFor="createdDate"
                      className="bg-tables absolute -top-2 left-3 px-[4px] py-[1px] text-[11px] text-[#76787A]"
                    >
                      Created Date
                    </label>
                  </div>

                  {/* Status (Active/Inactive) */}
                  <div className="relative w-full">
                    <select
                      id="status"
                      value={editIsActive ? "Active" : "Inactive"}
                      onChange={(e) =>
                        setEditIsActive(e.target.value === "Active")
                      }
                      className="peer w-full rounded-md border border-gray-300 px-3 pt-4 pb-2 text-sm text-[#050013] focus:ring-2 focus:ring-[#3324E3] focus:outline-none"
                    >
                      <option>Active</option>
                      <option>Inactive</option>
                    </select>
                    <label
                      htmlFor="status"
                      className="bg-tables absolute -top-2 left-3 px-[4px] py-[1px] text-[11px] text-[#76787A]"
                    >
                      Status
                    </label>
                  </div>
                </div>
              </div>

              {/* Access Control Section */}
              <div className="mb-16">
                <h3 className="mb-6 rounded-full bg-white px-4 py-2 text-[14px] font-medium text-[#050013]">
                  Access Control And Permissions
                </h3>
                <div className="px-2">
                  {accessSections.map((section, idx) => (
                    <AccordionSection
                      key={idx}
                      title={section.title}
                      items={section.items}
                      onPress={handleCheckboxPress}
                      /* ✅ IMPORTANT: pass actual selectedPermissions state */
                      selectedPermissions={selectedPermissions}
                    />
                  ))}
                </div>
              </div>
            </div>

            <div className="sticky bottom-0 border-t bg-white px-6 py-3">
              <button
                type="button"
                disabled={isUpdating}
                className="float-right rounded-full bg-[#3707EF] px-6 py-2 text-white hover:bg-[#3d0cc0] disabled:opacity-60"
                onClick={handleSaveEdit}
              >
                {isUpdating ? "Saving..." : "Save"}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
