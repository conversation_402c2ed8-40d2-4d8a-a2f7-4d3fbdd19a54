"use client";

import React, { useState } from "react";
import { FaChevronDown, FaCheck } from "react-icons/fa";
import Image from "next/image";
import { backendApiClient } from "@/utils/apiClient";
import { useMutation } from "@tanstack/react-query";
import { toast } from "react-toastify";
import { queryClient } from "@/hooks/useGlobalContext";

const permissionData = [
  {
    title: "User & Role Management",
    permissions: [
      {
        id: "17850a88-22e1-4258-99ab-1d23c3c15605",
        label: "Create, edit, delete, Dispatchers, Drivers, Users",
      },
      {
        id: "f9c41175-4790-4db3-ba3c-ea49178fe585",
        label: "Assign Admins to taxi companies",
      },
      {
        id: "b2d344a6-6104-4001-a646-b94c1dcff871",
        label: "Manage permissions for all roles",
      },
      {
        id: "896ae7ba-4b33-4c64-a229-3ffaeb2451ee",
        label: "Suspend/reactivate any user",
      },
    ],
  },
  {
    title: "Booking & Dispatch Control",
    permissions: [
      {
        id: "6a94ea9b-74f9-446a-b833-f77d8e57454d",
        label: "View, edit, and manage all bookings",
      },
      {
        id: "73e2a895-735f-4edf-bf71-2bfc73e4d3c2",
        label: "Assign/reassign drivers to rides",
      },
      {
        id: "912b2400-d29d-4367-a0bf-344d1f9bee73",
        label: "View driver live locations",
      },
      {
        id: "887f7358-e6a4-4e7e-b4a6-33b0754151e0",
        label: "Cancel or reschedule any ride",
      },
    ],
  },
  {
    title: "Company & Financial Management",
    permissions: [
      {
        id: "91f16da7-365a-4481-9601-7cc38ef9ce18",
        label: "Add, edit, or remove taxi companies",
      },
      {
        id: "cc025be7-6521-4b3f-83d1-984a107402b7",
        label: "Manage company subscription plans",
      },
      {
        id: "1c6f7aa9-6fed-44c3-b85a-cb0564030308",
        label: "View & modify company-specific pricing and fare structures",
      },
      {
        id: "63f54082-2563-4ede-8c91-840d73f7236f",
        label: "Access & edit company billing information",
      },
    ],
  },
  {
    title: "System & Policy Settings",
    permissions: [
      {
        id: "817105ed-b723-44f9-b02b-a2a553de0878",
        label: "Define platform-wide fare policies",
      },
      {
        id: "e060e981-76ca-4cf3-8b89-431ae7083f3b",
        label: "Set geofencing rules & restrictions",
      },
      {
        id: "8e3e677a-7f22-444e-8d5e-c4266b1225c3",
        label: "Control global discount and promo policies",
      },
      {
        id: "f2757189-f194-48e1-93fc-91e9945d0428",
        label: "Configure ride cancellation policies",
      },
    ],
  },
  {
    title: "Reporting & Analytics",
    permissions: [
      {
        id: "01af9c39-1eaf-4ade-8996-c236ea245a8e",
        label:
          "View and export reports on revenue, ride activity, and system performance",
      },
      {
        id: "08131c94-2341-4560-bc35-b671fb834fae",
        label: "Monitor driver performance & customer ratings",
      },
      {
        id: "28d21585-e20d-48cb-b364-b9bad38ff6c0",
        label: "Analyze dispatcher efficiency",
      },
    ],
  },
];

const InputField = (props) => (
  <input
    {...props}
    className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:ring-2 focus:ring-[#3324E3] focus:outline-none"
  />
);

const SelectField = ({ children, ...props }) => (
  <select
    {...props}
    className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:ring-2 focus:ring-[#3324E3] focus:outline-none"
  >
    {children}
  </select>
);

// Reusable Accordion Wrapper
const AccordionItem = ({ title, index, openStep, setOpenStep, children }) => (
  <div className="overflow-hidden rounded-sm">
    <button
      type="button"
      onClick={() => setOpenStep(openStep === index ? null : index)}
      className="bg-tables flex w-full items-center justify-between px-4 py-3 text-left transition hover:bg-gray-200"
    >
      <h2 className="text-[14px] font-medium text-[#050013]">{title}</h2>
      <FaChevronDown
        className={`text-[#76787A] transition-transform duration-200 ${openStep === index ? "rotate-180" : "rotate-0"}`}
      />
    </button>
    {openStep === index && <div className="bg-white p-4">{children}</div>}
  </div>
);
interface CustomCheckboxProps {
  id: string;
  label: string;
  isChecked?: boolean;
  onChange?: (id?: string) => void;
  name?: string;
  isDisabled?: boolean;
}
// Reusable Custom Checkbox
const CustomCheckbox = ({
  id,
  label,
  isChecked,
  onChange,
  name,
  isDisabled = false,
}: CustomCheckboxProps) => (
  <label
    htmlFor={id}
    className={`flex items-start gap-3 text-[14px] transition ${
      isDisabled
        ? "cursor-not-allowed text-gray-400"
        : "cursor-pointer text-[#76787A] hover:text-[#050013]"
    }`}
  >
    <div className="relative mt-1 flex items-center">
      <input
        id={id}
        type="checkbox"
        className="peer hidden"
        checked={isChecked}
        onChange={!isDisabled ? (e) => onChange?.(id) : undefined}
        disabled={isDisabled}
        name={name}
      />
      <span
        className={`flex h-4 w-4 shrink-0 items-center justify-center rounded-sm border transition-colors ${
          isChecked && !isDisabled
            ? "border-[#3324E3] bg-[#3324E3]"
            : "border-gray-400"
        } ${isDisabled ? "bg-gray-200" : ""}`}
      >
        <FaCheck
          size={10}
          className={`text-white transition-opacity ${isChecked ? "opacity-100" : "opacity-0"}`}
        />
      </span>
    </div>
    <span
      className={`peer-checked:text-[#050013] ${isDisabled ? "text-gray-400" : ""}`}
    >
      {label}
    </span>
  </label>
);

const FileUpload = ({ title, acceptedFormats, sampleFile, onFileSelect }) => (
  <div className="mt-1">
    <p className="pb-3 text-[14px] font-medium text-[#76787A]">{title}</p>
    <div className="rounded-lg border border-gray-200 bg-white p-4 shadow-none">
      <div className="flex w-full items-center justify-center">
        <label className="flex w-full cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 py-8 hover:bg-gray-50">
          <div className="flex flex-col items-center justify-center pt-5 pb-6">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="44"
              height="32"
              fill="none"
            >
              <path
                fill="#76787A"
                d="M21.818 0c3.348 0 6.705 1.265 9.258 3.818 1.981 1.981 3.161 4.46 3.606 7.03 5.07.824 8.954 5.192 8.954 10.485C43.636 27.213 38.85 32 32.97 32H9.212A9.213 9.213 0 0 1 0 22.788c0-4.952 3.918-8.956 8.818-9.167-.142-3.528 1.07-7.115 3.758-9.803A13.02 13.02 0 0 1 21.818 0m0 1.94a11.08 11.08 0 0 0-7.879 3.257 11.13 11.13 0 0 0-3.181 9.227.97.97 0 0 1-.97 1.091h-.576a7.236 7.236 0 0 0-7.273 7.273 7.236 7.236 0 0 0 7.273 7.273H32.97a8.713 8.713 0 0 0 8.727-8.728 8.72 8.72 0 0 0-7.924-8.697.97.97 0 0 1-.879-.848c-.28-2.41-1.33-4.74-3.182-6.591a11.12 11.12 0 0 0-7.894-3.258m0 9.696c.26.005.513.116.652.243l5.333 4.848c.399.347.418 1.001.076 1.38-.342.377-1.003.403-1.379.06l-3.712-3.38v10.425a.97.97 0 1 1-1.94 0V14.788l-3.712 3.379c-.376.343-1.018.3-1.378-.06-.374-.375-.304-1.04.075-1.38l5.334-4.848c.213-.195.394-.243.651-.243"
              />
            </svg>
            <p className="text-dark-grey text-sm">
              <span className="px-3 text-[14px]">
                Click or drag file to this area to upload
              </span>
            </p>
          </div>
          <input className="hidden" type="file" onChange={onFileSelect} />
        </label>
      </div>
    </div>
    <p className="py-3 text-[13px] text-[#76787A]">{acceptedFormats}</p>
    {sampleFile && (
      <div className="mt-3 flex w-full items-center justify-between rounded-[10px] bg-gray-50 p-3">
        <div>
          <p className="text-dark mb-1 text-sm font-semibold">
            {sampleFile.name}
          </p>
          <p className="text-sm text-gray-400">{sampleFile.details}</p>
        </div>
        <button
          type="button"
          className="flex items-center gap-2 font-semibold text-blue-800"
        >
          <svg
            stroke="currentColor"
            fill="currentColor"
            strokeWidth="0"
            viewBox="0 0 24 24"
            className="h-[20px] w-[20px]"
            height="1em"
            width="1em"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fill="none"
              strokeWidth="2"
              d="M2.99787498,0.********* L17.4999998,0.********* L20.9999998,4.50000005 L21,23 L3,23 L2.99787498,0.********* Z M16,1 L16,6 L21,6 M12,9 L12,18 M8,15 L12,19 L16,15"
            />
          </svg>
          <span className="text-sm">Download</span>
        </button>
      </div>
    )}
  </div>
);

// --- Main Component ---
export default function NewClient() {
  const [openGeneralStep, setOpenGeneralStep] = useState(0);
  const [openPermissionStep, setOpenPermissionStep] = useState(null);

  const [formData, setFormData] = useState({
    // Company Info
    companyName: "",
    legalName: "",
    companyRegistrationNumber: "",
    taxIdentificationNumber: "",
    businessType: "",
    websiteUrl: "",
    shortDescription: "",

    // Contact Info
    primaryContactName: "",
    primaryContactDesignation: "",
    primaryContactEmail: "",

    // Business Address
    headOfficeAddress: "",
    city: "",
    stateProvince: "",
    country: "",
    postalCode: "",

    // Payment & Billing
    billingCycle: "",
    invoiceEmail: "",
    legalRepresentativeName: "",

    // Service Details
    serviceType: "",
    operatingCities: "",
    fleetSize: "",
    availableVehicleTypes: "",
    wheelchairService: false,
    childSeatService: false,

    // Licensing & Compliance
    insuranceCompany: "",
    insurancePolicyNumber: "",
    insuranceExpiryDate: "",
    permitExpiryDate: "",
  });

   const [errors, setErrors] = useState({});

  const [files, setFiles] = useState({
    logo: null,
    contract: null,
    legalSignature: null,
    businessLicense: null,
    regulatoryCert: null,
  });

  const [checkedPermissions, setCheckedPermissions] = useState([]);

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const handleFileChange = (e, fileKey) => {
    const file = e.target.files?.[0];
    if (file) setFiles((prev) => ({ ...prev, [fileKey]: file }));
  };

  const handleCheckboxChange = (id) => {
    setCheckedPermissions((prev) =>
      prev.includes(id) ? prev.filter((pid) => pid !== id) : [...prev, id],
    );
  };

  async function addNewClient(data: any) {
    return await backendApiClient.post("clients", { json: data }).json();
  }

  const { mutate: addNewClientMutation } = useMutation({
    mutationFn: addNewClient,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["clients"] });
      setCheckedPermissions([]);
      setFormData({
        // Company Info
        companyName: "",
        legalName: "",
        companyRegistrationNumber: "",
        taxIdentificationNumber: "",
        businessType: "",
        websiteUrl: "",
        shortDescription: "",

        // Contact Info
        primaryContactName: "",
        primaryContactDesignation: "",
        primaryContactEmail: "",

        // Business Address
        headOfficeAddress: "",
        city: "",
        stateProvince: "",
        country: "",
        postalCode: "",

        // Payment & Billing
        billingCycle: "",
        invoiceEmail: "",
        legalRepresentativeName: "",

        // Service Details
        serviceType: "",
        operatingCities: "",
        fleetSize: "",
        availableVehicleTypes: "",
        wheelchairService: false,
        childSeatService: false,

        // Licensing & Compliance
        insuranceCompany: "",
        insurancePolicyNumber: "",
        insuranceExpiryDate: "",
        permitExpiryDate: "",
      });

      setFiles({
        logo: null,
        contract: null,
        legalSignature: null,
        businessLicense: null,
        regulatoryCert: null,
      });

      toast.success("Super Client Added Successfully", {
        autoClose: 5000,
        position: "top-center",
      });
    },
    onError: (err) => {
      console.error(err);
      toast.error("Failed to add Super Client");
    },
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    const submissionData = {
      clientDetails: formData,
      permissions: checkedPermissions,
      uploadedFiles: files,
    };

    console.log(submissionData, "ddddddddddddddddddddddddddd");

    // addNewClientMutation(submissionData);
  };

  return (
    <form
      onSubmit={handleSubmit}
      className="mx-auto max-w-4xl overflow-hidden rounded-[15px] p-4 sm:p-6 md:p-8"
    >
      <div className="mb-6 flex items-center gap-3">
        <Image src="/images/gen-info.svg" alt="Icon" width={48} height={48} />
        <h1 className="text-center text-[20px] font-medium text-[#050013]">
          General Info
        </h1>
      </div>

      <div className="space-y-4">
        {/* --- Company Information --- */}
        <AccordionItem
          title="Company Information"
          index={0}
          openStep={openGeneralStep}
          setOpenStep={setOpenGeneralStep}
        >
          <div className="space-y-4">
            <InputField
              name="companyName"
              value={formData.companyName}
              onChange={handleInputChange}
              placeholder="Company Name"
            />
            <InputField
              name="legalName"
              value={formData.legalName}
              onChange={handleInputChange}
              placeholder="Legal name(if different)"
            />
            <InputField
              name="companyRegistrationNumber"
              value={formData.companyRegistrationNumber}
              onChange={handleInputChange}
              placeholder="Company Registration Number"
            />
            <InputField
              name="taxIdentificationNumber"
              value={formData.taxIdentificationNumber}
              onChange={handleInputChange}
              placeholder="Tax Identification Number (TIN/VAT)"
            />
            <InputField
              name="businessType"
              value={formData.businessType}
              onChange={handleInputChange}
              placeholder="Enter Your Business Type"
            />

            <InputField
              name="websiteUrl"
              value={formData.websiteUrl}
              onChange={handleInputChange}
              type="url"
              placeholder="Website URL"
            />
            <FileUpload
              title="Upload a logo"
              acceptedFormats="Formats accepted are PNG & JPG"
              sampleFile={{ name: "Sample logo", details: "png 1.2MB" }}
              onFileSelect={(e) => handleFileChange(e, "logo")}
            />
            <textarea
              name="shortDescription"
              value={formData.shortDescription}
              onChange={handleInputChange}
              placeholder="Short Company Description"
              className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm"
              rows={3}
            ></textarea>
          </div>
        </AccordionItem>

        {/* --- Contact Information --- */}
        <AccordionItem
          title="Contact Information"
          index={1}
          openStep={openGeneralStep}
          setOpenStep={setOpenGeneralStep}
        >
          <div className="space-y-4">
            <InputField
              name="primaryContactName"
              value={formData.primaryContactName}
              onChange={handleInputChange}
              placeholder="Primary Contact Person Name"
            />
            <SelectField
              name="primaryContactDesignation"
              value={formData.primaryContactDesignation}
              onChange={handleInputChange}
            >
              <option value="" disabled>
                Designation
              </option>
              <option value="CEO">CEO</option>
              <option value="manager">Manager</option>
              <option value="owner">Owner</option>
            </SelectField>
            <InputField
              name="primaryContactEmail"
              value={formData.primaryContactEmail}
              onChange={handleInputChange}
              type="email"
              placeholder="Email Address"
            />
          </div>
        </AccordionItem>

        {/* --- Business Address --- */}
        <AccordionItem
          title="Business Address"
          index={2}
          openStep={openGeneralStep}
          setOpenStep={setOpenGeneralStep}
        >
          <div className="space-y-4">
            <InputField
              name="headOfficeAddress"
              value={formData.headOfficeAddress}
              onChange={handleInputChange}
              placeholder="Head Office Address"
            />
            <InputField
              name="city"
              value={formData.city}
              onChange={handleInputChange}
              placeholder="City"
            />
            <InputField
              name="stateProvince"
              value={formData.stateProvince}
              onChange={handleInputChange}
              placeholder="State/Province"
            />
            <InputField
              name="country"
              value={formData.country}
              onChange={handleInputChange}
              placeholder="Country"
            />
            <InputField
              name="postalCode"
              value={formData.postalCode}
              onChange={handleInputChange}
              placeholder="Postal Code"
            />
          </div>
        </AccordionItem>

        {/* --- Payment & Billing --- */}
        <AccordionItem
          title="Payment & Billing"
          index={3}
          openStep={openGeneralStep}
          setOpenStep={setOpenGeneralStep}
        >
          <div className="space-y-4">
            <SelectField
              name="billingCycle"
              value={formData.billingCycle}
              onChange={handleInputChange}
            >
              <option value="" disabled>
                Billing Cycle
              </option>
              <option value="monthly">Monthly</option>
              <option value="quarterly">Quarterly</option>
            </SelectField>
            <InputField
              name="invoiceEmail"
              value={formData.invoiceEmail}
              onChange={handleInputChange}
              type="email"
              placeholder="Invoice Email for Billing"
            />
            <FileUpload
              title="Contract Upload"
              acceptedFormats="Formats accepted are PNG & JPG"
              sampleFile={{ name: "Sample certificate", details: "png 1.2MB" }}
              onFileSelect={(e) => handleFileChange(e, "contract")}
            />
            <InputField
              name="legalRepresentativeName"
              value={formData.legalRepresentativeName}
              onChange={handleInputChange}
              placeholder="Legal Representative name"
            />
            <FileUpload
              title="Upload Legal Representative Signature"
              acceptedFormats="Formats accepted are png & jpg"
              onFileSelect={(e) => handleFileChange(e, "legalSignature")}
              sampleFile={{ name: "Sample certificate", details: "png 1.2MB" }}
            />
          </div>
        </AccordionItem>

        {/* --- Service Details --- */}
        <AccordionItem
          title="Service Details"
          index={4}
          openStep={openGeneralStep}
          setOpenStep={setOpenGeneralStep}
        >
          <div className="space-y-4">
            <InputField
              name="serviceType"
              value={formData.serviceType}
              onChange={handleInputChange}
              placeholder="Types of Services"
            />
            <InputField
              name="operatingCities"
              value={formData.operatingCities}
              onChange={handleInputChange}
              placeholder="Operating Cities/Regions"
            />
            <InputField
              name="fleetSize"
              value={formData.fleetSize}
              onChange={handleInputChange}
              type="number"
              placeholder="Fleet Size (Total number of vehicles)"
            />
            <InputField
              name="availableVehicleTypes"
              value={formData.availableVehicleTypes}
              onChange={handleInputChange}
              placeholder="Available Vehicle Types"
            />
            <div>
              <p className="mb-2 text-[13px] font-medium text-gray-600">
                Special Services
              </p>
              <div className="flex gap-6">
                <CustomCheckbox
                  id="wheelchairService"
                  name="wheelchairService"
                  label="Wheelchair Accessible"
                  isChecked={formData.wheelchairService}
                  onChange={handleInputChange}
                />
                <CustomCheckbox
                  id="childSeatService"
                  name="childSeatService"
                  label="Child Seat Available"
                  isChecked={formData.childSeatService}
                  onChange={handleInputChange}
                />
              </div>
            </div>
          </div>
        </AccordionItem>

        {/* --- Licensing & Compliance --- */}
        <AccordionItem
          title="Licensing & Compliance"
          index={5}
          openStep={openGeneralStep}
          setOpenStep={setOpenGeneralStep}
        >
          <div className="space-y-4">
            <FileUpload
              title="Business License"
              acceptedFormats="Formats accepted are PNG & JPG"
              sampleFile={{ name: "Sample License", details: "png 1.2MB" }}
              onFileSelect={(e) => handleFileChange(e, "businessLicense")}
            />
            <p className="pt-2 text-[14px] font-medium text-gray-700">
              Insurance Details
            </p>
            <InputField
              name="insuranceCompany"
              value={formData.insuranceCompany}
              onChange={handleInputChange}
              placeholder="Company"
            />
            <InputField
              name="insurancePolicyNumber"
              value={formData.insurancePolicyNumber}
              onChange={handleInputChange}
              placeholder="Policy Number"
            />
            <InputField
              name="insuranceExpiryDate"
              value={formData.insuranceExpiryDate}
              onChange={handleInputChange}
              type="date"
            />
            <FileUpload
              title="Regulatory Certificates (if applicable)"
              acceptedFormats="Formats accepted are PNG & JPG"
              sampleFile={{ name: "Sample Certificates", details: "png 1.2MB" }}
              onFileSelect={(e) => handleFileChange(e, "regulatoryCert")}
            />
            <InputField
              name="permitExpiryDate"
              value={formData.permitExpiryDate}
              onChange={handleInputChange}
              type="date"
            />
          </div>
        </AccordionItem>
      </div>

      {/* --- Give Permissions --- */}
      <div className="mt-8 mb-6 flex items-center gap-3">
        <Image
          src="/images/give-permission.svg"
          alt="Icon"
          width={48}
          height={48}
        />
        <h1 className="text-center text-[20px] font-medium text-[#050013]">
          Give Permissions
        </h1>
      </div>

      <div className="space-y-2">
        {permissionData.map((section, i) => (
          <AccordionItem
            key={section.title}
            title={section.title}
            index={i}
            openStep={openPermissionStep}
            setOpenStep={setOpenPermissionStep}
          >
            <div className="space-y-3 p-2">
              {section.permissions.map((perm) => (
                <CustomCheckbox
                  key={perm.id}
                  id={perm.id}
                  label={perm.label}
                  isChecked={checkedPermissions.includes(perm.id)}
                  onChange={() => handleCheckboxChange(perm.id)}
                />
              ))}
            </div>
          </AccordionItem>
        ))}
      </div>

      {/* --- Submit Button --- */}
      <div className="mt-8 text-center sm:text-right">
        <button
          type="submit"
          className="rounded-full bg-[#3324E3] px-8 py-2.5 font-medium text-white shadow-md transition hover:bg-[#291ed3] focus:ring-2 focus:ring-[#3324E3] focus:ring-offset-2 focus:outline-none"
        >
          Continue
        </button>
      </div>
    </form>
  );
}
